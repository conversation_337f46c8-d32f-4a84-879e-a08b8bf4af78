"use client"

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Eye, Target, TrendingUp } from "lucide-react"

interface GazeVisualizationPlaceholderProps {
  recordCount?: number
  avgStability?: number
  className?: string
}

export default function GazeVisualizationPlaceholder({ 
  recordCount = 0, 
  avgStability = 0,
  className = "" 
}: GazeVisualizationPlaceholderProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Eye className="h-5 w-5" />
          <span>注视稳定性数据可视化</span>
        </CardTitle>
        <CardDescription>
          实时显示患者注视轨迹和稳定性分析
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 统计信息 */}
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{recordCount}</div>
            <div className="text-sm text-muted-foreground">测试记录</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{avgStability.toFixed(1)}%</div>
            <div className="text-sm text-muted-foreground">平均稳定性</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">85%</div>
            <div className="text-sm text-muted-foreground">目标命中率</div>
          </div>
        </div>

        {/* 可视化占位图 */}
        <div className="relative w-full h-64 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
          <div className="text-center space-y-4">
            {/* 模拟注视轨迹图 */}
            <svg width="200" height="120" viewBox="0 0 200 120" className="mx-auto">
              {/* 目标点 */}
              <circle cx="100" cy="60" r="20" fill="none" stroke="#ef4444" strokeWidth="2" opacity="0.7" />
              <circle cx="100" cy="60" r="3" fill="#ef4444" />
              
              {/* 注视轨迹点 */}
              <circle cx="95" cy="58" r="2" fill="#3b82f6" opacity="0.8" />
              <circle cx="102" cy="62" r="2" fill="#3b82f6" opacity="0.8" />
              <circle cx="98" cy="59" r="2" fill="#3b82f6" opacity="0.8" />
              <circle cx="101" cy="61" r="2" fill="#3b82f6" opacity="0.8" />
              <circle cx="97" cy="63" r="2" fill="#3b82f6" opacity="0.8" />
              <circle cx="103" cy="58" r="2" fill="#3b82f6" opacity="0.8" />
              <circle cx="99" cy="60" r="2" fill="#3b82f6" opacity="0.8" />
              <circle cx="100" cy="59" r="2" fill="#10b981" opacity="0.8" />
              
              {/* 连接线 */}
              <path 
                d="M95,58 L102,62 L98,59 L101,61 L97,63 L103,58 L99,60 L100,59" 
                fill="none" 
                stroke="#6b7280" 
                strokeWidth="1" 
                opacity="0.4"
              />
              
              {/* 图例 */}
              <g transform="translate(10, 10)">
                <circle cx="5" cy="5" r="2" fill="#ef4444" />
                <text x="12" y="8" fontSize="8" fill="#374151">目标点</text>
                <circle cx="5" cy="18" r="2" fill="#3b82f6" />
                <text x="12" y="21" fontSize="8" fill="#374151">注视点</text>
                <circle cx="5" cy="31" r="2" fill="#10b981" />
                <text x="12" y="34" fontSize="8" fill="#374151">命中点</text>
              </g>
            </svg>
            
            <div className="space-y-2">
              <div className="flex items-center justify-center space-x-2">
                <Target className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium">注视轨迹可视化</span>
              </div>
              <p className="text-xs text-muted-foreground max-w-xs">
                显示患者注视点相对于目标点的分布情况，蓝色点为注视轨迹，绿色点为命中目标区域的点
              </p>
            </div>
          </div>
        </div>

        {/* 分析指标 */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">稳定性评分</span>
              <Badge variant="outline" className="text-xs">
                <TrendingUp className="h-3 w-3 mr-1" />
                优秀
              </Badge>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full" 
                style={{ width: `${Math.min(avgStability, 100)}%` }}
              ></div>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">数据完整性</span>
              <Badge variant="outline" className="text-xs">
                完整
              </Badge>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-green-500 to-emerald-500 h-2 rounded-full" 
                style={{ width: "95%" }}
              ></div>
            </div>
          </div>
        </div>

        {/* 提示信息 */}
        <div className="text-center text-xs text-muted-foreground bg-blue-50 p-3 rounded-lg">
          💡 点击"注视稳定性测试"按钮查看详细的数据分析和可视化图表
        </div>
      </CardContent>
    </Card>
  )
}
