import { NextRequest, NextResponse } from 'next/server'

// 目标服务器地址，从环境变量读取，默认为localhost:8080
const TARGET_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080'

// 处理所有HTTP方法的代理请求
async function handleProxy(request: NextRequest, { params }: { params: { path: string[] } }) {
  try {
    // 构建目标URL
    const path = params.path.join('/')
    const targetUrl = `${TARGET_URL}/${path}`
    
    // 获取查询参数
    const searchParams = request.nextUrl.searchParams
    const queryString = searchParams.toString()
    const fullUrl = queryString ? `${targetUrl}?${queryString}` : targetUrl

    // 获取请求体
    let body = null
    if (request.method !== 'GET' && request.method !== 'HEAD') {
      try {
        body = await request.text()
      } catch (error) {
        console.error('Error reading request body:', error)
      }
    }

    // 构建请求头，排除一些不需要的头部
    const headers: Record<string, string> = {}
    request.headers.forEach((value, key) => {
      // 排除一些可能导致问题的头部
      if (!['host', 'connection', 'content-length'].includes(key.toLowerCase())) {
        headers[key] = value
      }
    })

    // 确保Content-Type头部存在
    if (body && !headers['content-type']) {
      headers['content-type'] = 'application/json'
    }

    console.log(`Proxying ${request.method} request to: ${fullUrl}`)

    // 发送代理请求
    const response = await fetch(fullUrl, {
      method: request.method,
      headers,
      body: body || undefined,
    })

    // 获取响应数据
    const responseData = await response.text()
    
    // 构建响应头，添加CORS头部
    const responseHeaders = new Headers()
    
    // 复制原始响应头
    response.headers.forEach((value, key) => {
      responseHeaders.set(key, value)
    })

    // 添加CORS头部
    responseHeaders.set('Access-Control-Allow-Origin', '*')
    responseHeaders.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
    responseHeaders.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
    responseHeaders.set('Access-Control-Max-Age', '86400')

    return new NextResponse(responseData, {
      status: response.status,
      statusText: response.statusText,
      headers: responseHeaders,
    })

  } catch (error) {
    console.error('Proxy error:', error)
    return NextResponse.json(
      { 
        error: 'Proxy request failed', 
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now()
      },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
        }
      }
    )
  }
}

// 处理OPTIONS请求（预检请求）
export async function OPTIONS(request: NextRequest, context: { params: { path: string[] } }) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
      'Access-Control-Max-Age': '86400',
    },
  })
}

// 导出所有HTTP方法的处理函数
export async function GET(request: NextRequest, context: { params: { path: string[] } }) {
  return handleProxy(request, context)
}

export async function POST(request: NextRequest, context: { params: { path: string[] } }) {
  return handleProxy(request, context)
}

export async function PUT(request: NextRequest, context: { params: { path: string[] } }) {
  return handleProxy(request, context)
}

export async function DELETE(request: NextRequest, context: { params: { path: string[] } }) {
  return handleProxy(request, context)
}

export async function PATCH(request: NextRequest, context: { params: { path: string[] } }) {
  return handleProxy(request, context)
}
