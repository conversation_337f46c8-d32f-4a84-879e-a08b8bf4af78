"use client"

import React, { useState, useEffect } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import SaccadeAbilityList from './saccade-ability-list'
import SaccadeAbilityDetail from './saccade-ability-detail'
import { SaccadeAbilityRecord } from '@/lib/api'

export default function SaccadeAbilityPage() {
  const [currentView, setCurrentView] = useState<'list' | 'detail'>('list')
  const [selectedRecord, setSelectedRecord] = useState<SaccadeAbilityRecord | null>(null)
  const [recordIdFromUrl, setRecordIdFromUrl] = useState<number | null>(null)
  
  const searchParams = useSearchParams()
  const router = useRouter()

  // 监听URL参数变化
  useEffect(() => {
    const recordId = searchParams.get('recordId')
    if (recordId) {
      const id = parseInt(recordId, 10)
      if (!isNaN(id)) {
        setRecordIdFromUrl(id)
        setCurrentView('detail')
      }
    } else {
      setRecordIdFromUrl(null)
      setCurrentView('list')
    }
  }, [searchParams])

  const handleViewDetail = (record: SaccadeAbilityRecord) => {
    setSelectedRecord(record)
    setCurrentView('detail')
    // 更新URL，但不刷新页面
    router.push(`/saccade-ability?recordId=${record.recordId}`, { scroll: false })
  }

  const handleBackToList = () => {
    setSelectedRecord(null)
    setRecordIdFromUrl(null)
    setCurrentView('list')
    // 清除URL参数
    router.push('/saccade-ability', { scroll: false })
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {currentView === 'list' ? (
        <SaccadeAbilityList onViewDetail={handleViewDetail} />
      ) : (
        // 显示详情页面，优先使用URL中的recordId，其次使用选中的记录
        <SaccadeAbilityDetail
          recordId={recordIdFromUrl || selectedRecord?.recordId || 0}
          onBack={handleBackToList}
        />
      )}
    </div>
  )
}
