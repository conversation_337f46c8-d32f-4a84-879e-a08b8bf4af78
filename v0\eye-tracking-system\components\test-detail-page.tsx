"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import {
  ArrowLeft,
  Download,
  Share,
  Eye,
  Target,
  Move,
  Zap,
  Calendar,
  Clock,
  User,
  Activity,
  TrendingUp,
  AlertCircle,
} from "lucide-react"
import Link from "next/link"

// 生成详细测试数据
const generateDetailedTestData = (testId: string) => {
  const testType = testId.split("-")[0]
  const testNumber = testId.split("-")[1]

  const baseData = {
    id: testId,
    testId: `T${String(testNumber).padStart(3, "0")}`,
    patient: "张三",
    age: 28,
    gender: "男",
    date: "2024-01-15",
    time: "14:30:25",
    duration: 245,
    status: "completed",
    score: 85,
    type: testType,
  }

  const typeSpecificData = {
    fixation: {
      name: "注视稳定性测试",
      icon: Target,
      color: "bg-blue-500",
      metrics: {
        stability: 87.5,
        avgDuration: 1250,
        dispersion: 15.3,
        fixationCount: 45,
        blinkRate: 12,
        driftVelocity: 0.8,
      },
      visualization: {
        points: Array.from({ length: 100 }, (_, i) => ({
          x: 400 + Math.sin(i * 0.1) * 20 + (Math.random() - 0.5) * 10,
          y: 300 + Math.cos(i * 0.1) * 20 + (Math.random() - 0.5) * 10,
          timestamp: i * 50,
          duration: Math.random() * 200 + 100,
        })),
      },
      analysis: [
        { metric: "注视稳定性", value: "87.5%", status: "excellent", description: "注视点分布集中，稳定性良好" },
        { metric: "平均注视时长", value: "1.25s", status: "good", description: "注视持续时间在正常范围内" },
        { metric: "注视离散度", value: "15.3px", status: "good", description: "注视点散布程度适中" },
        { metric: "眨眼频率", value: "12次/分", status: "normal", description: "眨眼频率正常" },
      ],
    },
    pursuit: {
      name: "追随能力测试",
      icon: Move,
      color: "bg-green-500",
      metrics: {
        accuracy: 82.3,
        velocity: 156,
        lag: 85,
        gainHorizontal: 0.95,
        gainVertical: 0.92,
        smoothness: 78.5,
      },
      visualization: {
        targetPath: Array.from({ length: 200 }, (_, i) => ({
          x: 200 + Math.sin(i * 0.05) * 200,
          y: 300 + Math.cos(i * 0.03) * 150,
          timestamp: i * 25,
        })),
        gazePath: Array.from({ length: 200 }, (_, i) => ({
          x: 200 + Math.sin(i * 0.05) * 200 + (Math.random() - 0.5) * 30,
          y: 300 + Math.cos(i * 0.03) * 150 + (Math.random() - 0.5) * 25,
          timestamp: i * 25,
        })),
      },
      analysis: [
        { metric: "追随精度", value: "82.3%", status: "good", description: "眼球能较好地跟随目标运动" },
        { metric: "追随速度", value: "156°/s", status: "normal", description: "追随速度在正常范围" },
        { metric: "延迟时间", value: "85ms", status: "good", description: "反应延迟较小" },
        { metric: "运动平滑度", value: "78.5%", status: "good", description: "追随运动相对平滑" },
      ],
    },
    saccade: {
      name: "扫视能力测试",
      icon: Zap,
      color: "bg-purple-500",
      metrics: {
        frequency: 3.2,
        amplitude: 12.8,
        velocity: 385,
        accuracy: 91.2,
        latency: 195,
        overshoot: 8.5,
      },
      visualization: {
        movements: Array.from({ length: 15 }, (_, i) => ({
          start: {
            x: Math.random() * 600 + 100,
            y: Math.random() * 400 + 100,
          },
          end: {
            x: Math.random() * 600 + 100,
            y: Math.random() * 400 + 100,
          },
          duration: Math.random() * 50 + 30,
          velocity: Math.random() * 200 + 300,
          timestamp: i * 500,
        })),
      },
      analysis: [
        { metric: "扫视频率", value: "3.2次/秒", status: "normal", description: "扫视频率在正常范围" },
        { metric: "扫视幅度", value: "12.8°", status: "good", description: "扫视幅度适中" },
        { metric: "峰值速度", value: "385°/s", status: "excellent", description: "扫视速度良好" },
        { metric: "扫视精度", value: "91.2%", status: "excellent", description: "能准确到达目标位置" },
      ],
    },
    aoi: {
      name: "兴趣区域检测",
      icon: Eye,
      color: "bg-orange-500",
      metrics: {
        totalRegions: 5,
        avgFixationTime: 1850,
        maxFixationRegion: "区域A",
        scanPathLength: 2340,
        revisitRate: 23.5,
        firstFixationLatency: 280,
      },
      visualization: {
        regions: [
          {
            id: 1,
            name: "区域A",
            x: 100,
            y: 100,
            width: 200,
            height: 150,
            fixations: 25,
            duration: 4200,
            percentage: 35.2,
          },
          {
            id: 2,
            name: "区域B",
            x: 350,
            y: 200,
            width: 180,
            height: 120,
            fixations: 18,
            duration: 2800,
            percentage: 23.4,
          },
          {
            id: 3,
            name: "区域C",
            x: 200,
            y: 350,
            width: 220,
            height: 100,
            fixations: 15,
            duration: 2100,
            percentage: 17.6,
          },
          {
            id: 4,
            name: "区域D",
            x: 450,
            y: 80,
            width: 150,
            height: 180,
            fixations: 12,
            duration: 1650,
            percentage: 13.8,
          },
          {
            id: 5,
            name: "区域E",
            x: 50,
            y: 400,
            width: 180,
            height: 120,
            fixations: 8,
            duration: 1200,
            percentage: 10.0,
          },
        ],
        scanPath: Array.from({ length: 50 }, (_, i) => ({
          x: Math.random() * 700 + 50,
          y: Math.random() * 500 + 50,
          duration: Math.random() * 300 + 100,
          order: i + 1,
        })),
      },
      analysis: [
        { metric: "兴趣区域数", value: "5个", status: "normal", description: "识别到的兴趣区域数量适中" },
        { metric: "平均注视时长", value: "1.85s", status: "good", description: "对兴趣区域的注视时间充足" },
        { metric: "主要关注区域", value: "区域A (35.2%)", status: "normal", description: "注意力分布相对集中" },
        { metric: "重访率", value: "23.5%", status: "normal", description: "对重要区域有适度的重复关注" },
      ],
    },
  }

  return {
    ...baseData,
    ...typeSpecificData[testType as keyof typeof typeSpecificData],
  }
}

interface TestDetailPageProps {
  testId: string
}

export default function TestDetailPage({ testId }: TestDetailPageProps) {
  const [testData, setTestData] = useState<any>(null)
  const [activeTab, setActiveTab] = useState("overview")

  useEffect(() => {
    setTestData(generateDetailedTestData(testId))
  }, [testId])

  if (!testData) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>加载测试数据中...</p>
        </div>
      </div>
    )
  }

  const renderVisualization = () => {
    const svgWidth = 800
    const svgHeight = 600

    switch (testData.type) {
      case "fixation":
        return (
          <svg width={svgWidth} height={svgHeight} className="border rounded-lg bg-gray-50">
            <circle cx={400} cy={300} r={50} fill="none" stroke="#ef4444" strokeWidth={2} strokeDasharray="5,5" />
            <circle cx={400} cy={300} r={25} fill="none" stroke="#f97316" strokeWidth={1} strokeDasharray="3,3" />
            {testData.visualization.points.map((point: any, i: number) => (
              <circle
                key={i}
                cx={point.x}
                cy={point.y}
                r={Math.max(2, point.duration / 50)}
                fill="#3b82f6"
                opacity={0.6}
              />
            ))}
            <text x={20} y={30} className="text-sm font-medium fill-gray-700">
              注视稳定性分析 - 红圈为目标区域，蓝点为实际注视点
            </text>
          </svg>
        )

      case "pursuit":
        return (
          <svg width={svgWidth} height={svgHeight} className="border rounded-lg bg-gray-50">
            <path
              d={`M ${testData.visualization.targetPath.map((p: any) => `${p.x},${p.y}`).join(" L ")}`}
              fill="none"
              stroke="#ef4444"
              strokeWidth={3}
              opacity={0.8}
            />
            <path
              d={`M ${testData.visualization.gazePath.map((p: any) => `${p.x},${p.y}`).join(" L ")}`}
              fill="none"
              stroke="#3b82f6"
              strokeWidth={2}
            />
            <text x={20} y={30} className="text-sm font-medium fill-gray-700">
              追随能力分析 - 红线：目标轨迹，蓝线：眼球轨迹
            </text>
          </svg>
        )

      case "saccade":
        return (
          <svg width={svgWidth} height={svgHeight} className="border rounded-lg bg-gray-50">
            {testData.visualization.movements.map((movement: any, i: number) => (
              <g key={i}>
                <line
                  x1={movement.start.x}
                  y1={movement.start.y}
                  x2={movement.end.x}
                  y2={movement.end.y}
                  stroke="#8b5cf6"
                  strokeWidth={Math.max(1, movement.velocity / 200)}
                  markerEnd="url(#arrowhead)"
                />
                <circle cx={movement.start.x} cy={movement.start.y} r={4} fill="#10b981" />
                <circle cx={movement.end.x} cy={movement.end.y} r={4} fill="#ef4444" />
                <text x={movement.start.x + 5} y={movement.start.y - 5} className="text-xs fill-gray-600">
                  {i + 1}
                </text>
              </g>
            ))}
            <defs>
              <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#8b5cf6" />
              </marker>
            </defs>
            <text x={20} y={30} className="text-sm font-medium fill-gray-700">
              扫视能力分析 - 绿点：起始位置，红点：结束位置，线条粗细表示速度
            </text>
          </svg>
        )

      case "aoi":
        return (
          <svg width={svgWidth} height={svgHeight} className="border rounded-lg bg-gray-50">
            {testData.visualization.regions.map((region: any) => (
              <g key={region.id}>
                <rect
                  x={region.x}
                  y={region.y}
                  width={region.width}
                  height={region.height}
                  fill="#3b82f6"
                  opacity={region.percentage / 100}
                  stroke="#3b82f6"
                  strokeWidth={2}
                />
                <text
                  x={region.x + region.width / 2}
                  y={region.y + region.height / 2 - 10}
                  textAnchor="middle"
                  className="text-sm font-medium fill-gray-800"
                >
                  {region.name}
                </text>
                <text
                  x={region.x + region.width / 2}
                  y={region.y + region.height / 2 + 10}
                  textAnchor="middle"
                  className="text-xs fill-gray-600"
                >
                  {region.percentage}% ({region.fixations}次)
                </text>
              </g>
            ))}
            {testData.visualization.scanPath.slice(0, 20).map((point: any, i: number) => (
              <g key={i}>
                <circle cx={point.x} cy={point.y} r={3} fill="#f59e0b" opacity={0.7} />
                <text x={point.x + 5} y={point.y - 5} className="text-xs fill-gray-500">
                  {point.order}
                </text>
              </g>
            ))}
            <text x={20} y={30} className="text-sm font-medium fill-gray-700">
              兴趣区域分析 - 区域透明度表示关注度，黄点显示扫视路径
            </text>
          </svg>
        )

      default:
        return null
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "excellent":
        return "text-green-600"
      case "good":
        return "text-blue-600"
      case "normal":
        return "text-yellow-600"
      case "poor":
        return "text-red-600"
      default:
        return "text-gray-600"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "excellent":
        return <TrendingUp className="w-4 h-4 text-green-600" />
      case "good":
        return <Activity className="w-4 h-4 text-blue-600" />
      case "normal":
        return <AlertCircle className="w-4 h-4 text-yellow-600" />
      case "poor":
        return <AlertCircle className="w-4 h-4 text-red-600" />
      default:
        return null
    }
  }

  return (
    <div className="min-h-screen bg-gray-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/">
              <Button variant="outline" size="icon">
                <ArrowLeft className="w-4 h-4" />
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <testData.icon className="w-8 h-8" />
                {testData.name}
              </h1>
              <p className="text-gray-600 mt-1">
                测试ID: {testData.testId} • 患者: {testData.patient}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <User className="w-4 h-4" />
              <span>
                {typeof window !== "undefined" && localStorage.getItem("eyeTrackingUser")
                  ? JSON.parse(localStorage.getItem("eyeTrackingUser")!).username
                  : "用户"}
              </span>
            </div>
            <Badge variant="default" className="px-3 py-1">
              评分: {testData.score}/100
            </Badge>
            <Button variant="outline" className="flex items-center gap-2">
              <Share className="w-4 h-4" />
              分享
            </Button>
            <Button className="flex items-center gap-2">
              <Download className="w-4 h-4" />
              导出报告
            </Button>
          </div>
        </div>

        {/* Patient Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="w-5 h-5" />
              患者信息
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div>
                <p className="text-sm text-gray-600">姓名</p>
                <p className="font-medium">{testData.patient}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">年龄</p>
                <p className="font-medium">{testData.age}岁</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">性别</p>
                <p className="font-medium">{testData.gender}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">测试日期</p>
                <p className="font-medium flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  {testData.date}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">测试时间</p>
                <p className="font-medium flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  {testData.time}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">测试时长</p>
                <p className="font-medium">
                  {Math.floor(testData.duration / 60)}分{testData.duration % 60}秒
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">测试状态</p>
                <Badge variant="default">已完成</Badge>
              </div>
              <div>
                <p className="text-sm text-gray-600">综合评分</p>
                <p
                  className={`font-medium text-lg ${
                    testData.score >= 80 ? "text-green-600" : testData.score >= 60 ? "text-yellow-600" : "text-red-600"
                  }`}
                >
                  {testData.score}/100
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">测试概览</TabsTrigger>
            <TabsTrigger value="visualization">数据可视化</TabsTrigger>
            <TabsTrigger value="analysis">详细分析</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Object.entries(testData.metrics).map(([key, value]) => (
                <Card key={key}>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium capitalize">
                      {key.replace(/([A-Z])/g, " $1").trim()}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{value}</div>
                    <Progress value={Math.random() * 100} className="mt-2" />
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="visualization" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>数据可视化</CardTitle>
                <CardDescription>{testData.name}的详细可视化分析结果</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex justify-center">{renderVisualization()}</div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analysis" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {testData.analysis.map((item: any, index: number) => (
                <Card key={index}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{item.metric}</CardTitle>
                      {getStatusIcon(item.status)}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className={`text-2xl font-bold mb-2 ${getStatusColor(item.status)}`}>{item.value}</div>
                    <p className="text-sm text-gray-600">{item.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
