import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { getApiBaseURL, config, debugLog } from './config'

// API响应的通用接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp?: number
}

// 分页响应接口
export interface PageResponse<T = any> {
  current: number
  size: number
  total: number
  pages: number
  records: T[]
}

// 注视稳定性测试数据接口
export interface GazeStabilityRecord {
  id: number
  recordId: number
  patientId: number
  patientName: string
  inpatientNum: string
  caseCardNum: string
  deviceId: number
  deviceName: string | null
  deviceSn: string
  operatorId: number
  operatorName: string | null
  testType: string
  testSequence: string
  testDate: string
  duration: number
  status: string
  statusDesc: string
  targetX: number
  targetY: number
  gazeTrajectoryJson: string
  stabilityScore: number | null
  evaluationResult: string | null
  deviationX: number | null
  deviationY: number | null
  fixationDuration: number | null
  fixationCount: number | null
  averageDistance: number | null
  maxDeviation: number | null
  rmsError: number | null
  targetPointRadius: number
  gazePointRadius: number
  loopRadiusIncreases: number
  loopsCount: number
  calibrationParams: string
  environmentInfo: string
  notes: string
  imageUrl: string | null
  createdAt: string
  updatedAt: string
}

// 注视轨迹点接口
export interface GazePoint {
  x: number
  y: number
  index: number
  distance: number
  duration: number
  direction: string
  ringLevel: number
  directionAngle: number
  isInTargetArea: boolean
  relativeDistance: number
}

// 扫视能力测试数据接口
export interface SaccadeAbilityRecord {
  id: number
  recordId: number
  patientId: number
  patientName: string
  inpatientNum: string
  caseCardNum: string
  deviceId: number
  deviceName: string | null
  deviceSn: string
  operatorId: number
  operatorName: string | null
  testType: string
  testSequence: string
  testDate: string
  duration: number
  status: string
  statusDesc: string
  targetPoints: string
  saccadeEvents: string | null
  gazeTrajectoryJson: string
  totalSaccades: number
  successfulSaccades: number
  accuracyRate: number
  averageSaccadeTime: number
  saccadeVelocity: number
  errorDistance: number
  latency: number
  peakVelocity: number
  undershootRate: number
  overshootRate: number
  fixationStability: number
  calibrationParams: string
  environmentInfo: string
  notes: string
  imageUrl: string | null
  createdAt: string
  updatedAt: string
}

// 扫视轨迹点接口
export interface SaccadePoint {
  x: number
  y: number
  index: number
  accuracy: number
  distance: number
  duration: number
  velocity: number
  errorType: string
  isOnTarget: boolean
  saccadeType: string
  targetIndex: number
  isValidSaccade: boolean
  saccadeQuality: string
  distanceToTarget: number
}

// 分页查询参数接口
export interface PageParams {
  current: number
  size: number
  patientName?: string
  testType?: string
  status?: string
  startDate?: string
  endDate?: string
}

class ApiService {
  private instance: AxiosInstance

  constructor() {
    const baseURL = getApiBaseURL()
    debugLog('API Service initialized with baseURL:', baseURL)

    this.instance = axios.create({
      baseURL,
      timeout: config.api.timeout,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        debugLog('API Request:', config.method?.toUpperCase(), config.url, config.data)

        // 可以在这里添加认证token等
        const token = localStorage.getItem('authToken')
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      (error) => {
        debugLog('API Request Error:', error)
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        debugLog('API Response:', response.status, response.config.url, response.data)

        const { data } = response
        if (data.code === 200) {
          return response
        } else {
          // 处理业务错误
          debugLog('API Business Error:', data)
          throw new Error(data.message || '请求失败')
        }
      },
      (error) => {
        debugLog('API Response Error:', error)

        // 处理HTTP错误
        if (error.response) {
          const { status, data } = error.response
          switch (status) {
            case 401:
              // 未授权，跳转到登录页
              if (typeof window !== 'undefined') {
                window.location.href = '/login'
              }
              break
            case 403:
              throw new Error('没有权限访问')
            case 404:
              throw new Error('请求的资源不存在')
            case 500:
              throw new Error('服务器内部错误')
            default:
              throw new Error(data?.message || '请求失败')
          }
        } else if (error.request) {
          throw new Error('网络连接失败')
        } else {
          throw new Error('请求配置错误')
        }
        return Promise.reject(error)
      }
    )
  }

  // 通用GET请求
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.get<ApiResponse<T>>(url, config)
    return response.data
  }

  // 通用POST请求
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.post<ApiResponse<T>>(url, data, config)
    return response.data
  }

  // 通用PUT请求
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.put<ApiResponse<T>>(url, data, config)
    return response.data
  }

  // 通用DELETE请求
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.delete<ApiResponse<T>>(url, config)
    return response.data
  }

  // 注视稳定性测试相关API

  // 获取注视稳定性测试列表（分页）
  async getGazeStabilityList(params: PageParams): Promise<ApiResponse<PageResponse<GazeStabilityRecord>>> {
    return this.post('/api/movement/gaze-stability/page', params)
  }

  // 获取注视稳定性测试详情
  async getGazeStabilityDetail(recordId: number): Promise<ApiResponse<GazeStabilityRecord>> {
    return this.get(`/api/movement/gaze-stability/record/${recordId}`)
  }

  // 创建注视稳定性测试
  async createGazeStabilityTest(data: Partial<GazeStabilityRecord>): Promise<ApiResponse<GazeStabilityRecord>> {
    return this.post('/api/movement/gaze-stability', data)
  }

  // 更新注视稳定性测试
  async updateGazeStabilityTest(id: number, data: Partial<GazeStabilityRecord>): Promise<ApiResponse<GazeStabilityRecord>> {
    return this.put(`/api/movement/gaze-stability/${id}`, data)
  }

  // 删除注视稳定性测试
  async deleteGazeStabilityTest(id: number): Promise<ApiResponse<void>> {
    return this.delete(`/api/movement/gaze-stability/${id}`)
  }

  // 扫视能力测试相关API

  // 获取扫视能力测试列表（分页）
  async getSaccadeAbilityList(params: PageParams): Promise<ApiResponse<PageResponse<SaccadeAbilityRecord>>> {
    return this.post('/api/movement/saccade-ability/page', params)
  }

  // 获取扫视能力测试详情
  async getSaccadeAbilityDetail(recordId: number): Promise<ApiResponse<SaccadeAbilityRecord>> {
    return this.get(`/api/movement/saccade-ability/record/${recordId}`)
  }

  // 创建扫视能力测试
  async createSaccadeAbilityTest(data: Partial<SaccadeAbilityRecord>): Promise<ApiResponse<SaccadeAbilityRecord>> {
    return this.post('/api/movement/saccade-ability', data)
  }

  // 更新扫视能力测试
  async updateSaccadeAbilityTest(id: number, data: Partial<SaccadeAbilityRecord>): Promise<ApiResponse<SaccadeAbilityRecord>> {
    return this.put(`/api/movement/saccade-ability/${id}`, data)
  }

  // 删除扫视能力测试
  async deleteSaccadeAbilityTest(id: number): Promise<ApiResponse<void>> {
    return this.delete(`/api/movement/saccade-ability/${id}`)
  }
}

// 创建API服务实例
const apiService = new ApiService()

export default apiService
