// 应用配置
export const config = {
  // API配置
  api: {
    baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080',
    timeout: 10000,
    // 是否使用代理（在浏览器环境中使用代理避免跨域）
    useProxy: typeof window !== 'undefined',
  },
  
  // 应用信息
  app: {
    name: process.env.NEXT_PUBLIC_APP_NAME || '眼球运动评估系统',
    version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
  },
  
  // 开发配置
  dev: {
    isDevelopment: process.env.NODE_ENV === 'development',
    enableDebugLog: process.env.NODE_ENV === 'development',
  }
}

// 获取API基础URL
export const getApiBaseURL = () => {
  // 在浏览器环境中使用代理
  if (typeof window !== 'undefined') {
    return '/api/proxy'
  }
  // 在服务器环境中直接使用目标URL
  return config.api.baseURL
}

// 调试日志
export const debugLog = (...args: any[]) => {
  if (config.dev.enableDebugLog) {
    console.log('[DEBUG]', ...args)
  }
}
