"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import {
  User,
  Calendar,
  Clock,
  Activity,
  Target,
  Eye,
  Settings,
  FileText,
  BarChart3,
  Home,
  Zap,
  TrendingUp,
  Timer
} from "lucide-react"
import { useRouter } from "next/navigation"
import { useToast } from "@/hooks/use-toast"
import apiService, { SaccadeAbilityRecord, SaccadePoint } from "@/lib/api"

interface SaccadeAbilityDetailProps {
  recordId: number
  onBack?: () => void
}

export default function SaccadeAbilityDetail({ recordId, onBack }: SaccadeAbilityDetailProps) {
  const [record, setRecord] = useState<SaccadeAbilityRecord | null>(null)
  const [saccadePoints, setSaccadePoints] = useState<SaccadePoint[]>([])
  const [loading, setLoading] = useState(true)
  const router = useRouter()
  const { toast } = useToast()

  // 获取详情数据
  const fetchDetail = async () => {
    setLoading(true)
    try {
      const response = await apiService.getSaccadeAbilityDetail(recordId)
      setRecord(response.data)
      
      // 解析扫视轨迹数据
      if (response.data.gazeTrajectoryJson) {
        try {
          const points = JSON.parse(response.data.gazeTrajectoryJson)
          // 确保解析的数据是数组
          if (Array.isArray(points)) {
            setSaccadePoints(points as SaccadePoint[])
          } else {
            console.error('扫视轨迹数据不是数组格式:', points)
            setSaccadePoints([])
          }
        } catch (error) {
          console.error('解析扫视轨迹数据失败:', error)
          setSaccadePoints([])
        }
      } else {
        setSaccadePoints([])
      }
    } catch (error) {
      toast({
        title: "获取详情失败",
        description: error instanceof Error ? error.message : "请检查网络连接",
        variant: "destructive",
      })
      console.error('获取扫视能力测试详情失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (recordId) {
      fetchDetail()
    }
  }, [recordId])

  // 状态颜色映射
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <Badge className="bg-green-100 text-green-800">已完成</Badge>
      case 'IN_PROGRESS':
        return <Badge className="bg-blue-100 text-blue-800">进行中</Badge>
      case 'FAILED':
        return <Badge variant="destructive">失败</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  // 格式化时长
  const formatDuration = (duration: number) => {
    const minutes = Math.floor(duration / 60000)
    const seconds = Math.floor((duration % 60000) / 1000)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  // 获取扫视质量颜色
  const getSaccadeQualityColor = (quality: string) => {
    switch (quality) {
      case 'GOOD':
        return 'text-green-600'
      case 'FAIR':
        return 'text-yellow-600'
      case 'POOR':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  // 获取扫视类型标签
  const getSaccadeTypeLabel = (type: string) => {
    switch (type) {
      case 'INITIAL':
        return '初始'
      case 'NORMAL_SACCADE':
        return '正常扫视'
      case 'LARGE_SACCADE':
        return '大幅扫视'
      case 'FINAL':
        return '结束'
      default:
        return type
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        <span className="ml-2">加载详情数据...</span>
      </div>
    )
  }

  if (!record) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">未找到测试记录</p>
        <Button onClick={onBack} className="mt-4">
          返回列表
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">扫视能力测试详情</h1>
          <p className="text-muted-foreground">
            测试序号: {record.testSequence} | 患者: {record.patientName}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={onBack}>
            <Home className="w-4 h-4 mr-2" />
            返回列表
          </Button>
        </div>
      </div>

      {/* 基本信息卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <User className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm font-medium">患者信息</p>
                <p className="text-lg font-bold">{record.patientName}</p>
                <p className="text-xs text-gray-500">住院号: {record.inpatientNum}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm font-medium">测试时间</p>
                <p className="text-lg font-bold">{formatDate(record.testDate)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-orange-600" />
              <div>
                <p className="text-sm font-medium">测试时长</p>
                <p className="text-lg font-bold">{formatDuration(record.duration)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Activity className="h-4 w-4 text-purple-600" />
              <div>
                <p className="text-sm font-medium">测试状态</p>
                <div className="mt-1">{getStatusBadge(record.status)}</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 详细信息标签页 */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            测试概览
          </TabsTrigger>
          <TabsTrigger value="trajectory" className="flex items-center gap-2">
            <Target className="w-4 h-4" />
            扫视轨迹
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            测试配置
          </TabsTrigger>
          <TabsTrigger value="notes" className="flex items-center gap-2">
            <FileText className="w-4 h-4" />
            备注信息
          </TabsTrigger>
        </TabsList>

        {/* 测试概览 */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base flex items-center gap-2">
                  <Zap className="w-4 h-4 text-purple-600" />
                  扫视统计
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">总扫视次数</span>
                  <span className="font-medium">{record.totalSaccades}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">成功扫视</span>
                  <span className="font-medium text-green-600">{record.successfulSaccades}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">准确率</span>
                  <span className={`font-medium ${
                    record.accuracyRate >= 80 ? 'text-green-600' :
                    record.accuracyRate >= 60 ? 'text-yellow-600' : 'text-red-600'
                  }`}>
                    {record.accuracyRate.toFixed(1)}%
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base flex items-center gap-2">
                  <Timer className="w-4 h-4 text-blue-600" />
                  时间指标
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">平均扫视时间</span>
                  <span className="font-medium">{record.averageSaccadeTime.toFixed(1)}ms</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">反应延迟</span>
                  <span className="font-medium">{record.latency.toFixed(1)}ms</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">峰值速度</span>
                  <span className="font-medium">{record.peakVelocity.toFixed(0)}°/s</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base flex items-center gap-2">
                  <TrendingUp className="w-4 h-4 text-orange-600" />
                  精度指标
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">扫视速度</span>
                  <span className="font-medium">{record.saccadeVelocity.toFixed(0)}°/s</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">误差距离</span>
                  <span className="font-medium">{record.errorDistance.toFixed(1)}px</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">注视稳定性</span>
                  <span className="font-medium">{record.fixationStability.toFixed(1)}%</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base flex items-center gap-2">
                  <Target className="w-4 h-4 text-red-600" />
                  误差分析
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">欠射率</span>
                  <span className="font-medium text-yellow-600">{record.undershootRate.toFixed(1)}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">过射率</span>
                  <span className="font-medium text-orange-600">{record.overshootRate.toFixed(1)}%</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 扫视轨迹 */}
        <TabsContent value="trajectory" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5" />
                扫视轨迹数据
              </CardTitle>
              <CardDescription>
                共记录 {saccadePoints.length} 个扫视点
              </CardDescription>
            </CardHeader>
            <CardContent>
              {Array.isArray(saccadePoints) && saccadePoints.length > 0 ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
                    {saccadePoints.map((point, index) => {
                      if (!point || typeof point.x !== 'number') return null
                      return (
                        <div key={index} className="border rounded-lg p-3 bg-gray-50">
                          <div className="flex items-center justify-between mb-2">
                            <span className="font-medium">扫视点 {point.index}</span>
                            <Badge variant={point.isValidSaccade ? "default" : "secondary"}>
                              {point.isValidSaccade ? "有效" : "无效"}
                            </Badge>
                          </div>
                          <div className="grid grid-cols-2 gap-2 text-sm">
                            <div>
                              <span className="text-gray-600">坐标:</span>
                              <span className="ml-1">({point.x.toFixed(3)}, {point.y.toFixed(3)})</span>
                            </div>
                            <div>
                              <span className="text-gray-600">准确度:</span>
                              <span className="ml-1">{point.accuracy.toFixed(1)}%</span>
                            </div>
                            <div>
                              <span className="text-gray-600">持续时间:</span>
                              <span className="ml-1">{point.duration}ms</span>
                            </div>
                            <div>
                              <span className="text-gray-600">速度:</span>
                              <span className="ml-1">{point.velocity.toFixed(1)}°/s</span>
                            </div>
                            <div>
                              <span className="text-gray-600">类型:</span>
                              <span className="ml-1">{getSaccadeTypeLabel(point.saccadeType)}</span>
                            </div>
                            <div>
                              <span className="text-gray-600">质量:</span>
                              <span className={`ml-1 ${getSaccadeQualityColor(point.saccadeQuality)}`}>
                                {point.saccadeQuality}
                              </span>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  暂无扫视轨迹数据
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 测试配置 */}
        <TabsContent value="settings" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">设备信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">设备ID</span>
                  <span className="font-medium">{record.deviceId}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">设备序列号</span>
                  <span className="font-medium font-mono text-xs">{record.deviceSn}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">设备名称</span>
                  <span className="font-medium">{record.deviceName || '未设置'}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">测试参数</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">测试类型</span>
                  <span className="font-medium">{record.testType}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">测试序号</span>
                  <span className="font-medium">{record.testSequence}</span>
                </div>
                <div className="text-sm">
                  <span className="text-gray-600">校准参数:</span>
                  <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                    {record.calibrationParams}
                  </pre>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 备注信息 */}
        <TabsContent value="notes" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">环境信息</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="text-sm whitespace-pre-wrap bg-gray-50 p-3 rounded">
                  {record.environmentInfo}
                </pre>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">测试备注</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm whitespace-pre-wrap">
                  {record.notes || '无备注信息'}
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="text-base">记录信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">创建时间</span>
                <span className="font-medium">{formatDate(record.createdAt)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">更新时间</span>
                <span className="font-medium">{formatDate(record.updatedAt)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">操作员</span>
                <span className="font-medium">{record.operatorName || '未设置'}</span>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
