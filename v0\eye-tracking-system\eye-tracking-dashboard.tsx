"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Eye, Target, Move, Zap, Play, Pause, RotateCcw, Settings } from "lucide-react"

// 模拟眼球运动数据
const generateMockData = () => ({
  fixation: {
    stability: Math.random() * 100,
    duration: Math.random() * 5000 + 1000,
    dispersion: Math.random() * 50,
    points: Array.from({ length: 50 }, (_, i) => ({
      x: 400 + Math.random() * 40 - 20,
      y: 300 + Math.random() * 40 - 20,
      timestamp: i * 20,
    })),
  },
  pursuit: {
    accuracy: Math.random() * 100,
    velocity: Math.random() * 200 + 50,
    lag: Math.random() * 100,
    path: Array.from({ length: 100 }, (_, i) => ({
      target: { x: 200 + Math.sin(i * 0.1) * 150, y: 300 + Math.cos(i * 0.05) * 100 },
      gaze: {
        x: 200 + Math.sin(i * 0.1) * 150 + (Math.random() - 0.5) * 20,
        y: 300 + Math.cos(i * 0.05) * 100 + (Math.random() - 0.5) * 20,
      },
      timestamp: i * 50,
    })),
  },
  saccade: {
    frequency: Math.random() * 5 + 1,
    amplitude: Math.random() * 20 + 5,
    velocity: Math.random() * 500 + 200,
    movements: Array.from({ length: 10 }, (_, i) => ({
      start: { x: Math.random() * 600 + 100, y: Math.random() * 400 + 100 },
      end: { x: Math.random() * 600 + 100, y: Math.random() * 400 + 100 },
      duration: Math.random() * 100 + 50,
      timestamp: i * 1000,
    })),
  },
  aoi: {
    regions: [
      { id: 1, name: "区域A", x: 100, y: 100, width: 200, height: 150, fixations: 15, duration: 3200 },
      { id: 2, name: "区域B", x: 350, y: 200, width: 180, height: 120, fixations: 8, duration: 1800 },
      { id: 3, name: "区域C", x: 200, y: 350, width: 220, height: 100, fixations: 12, duration: 2500 },
    ],
    heatmap: Array.from({ length: 40 }, (_, i) => Array.from({ length: 30 }, (_, j) => Math.random() * 100)),
  },
})

export default function EyeTrackingDashboard() {
  const [activeTab, setActiveTab] = useState("fixation")
  const [isRecording, setIsRecording] = useState(false)
  const [data, setData] = useState(generateMockData())

  useEffect(() => {
    const interval = setInterval(() => {
      if (isRecording) {
        setData(generateMockData())
      }
    }, 1000)
    return () => clearInterval(interval)
  }, [isRecording])

  const renderVisualization = (type: string) => {
    const svgWidth = 800
    const svgHeight = 600

    switch (type) {
      case "fixation":
        return (
          <svg width={svgWidth} height={svgHeight} className="border rounded-lg bg-gray-50">
            <circle cx={400} cy={300} r={30} fill="none" stroke="#ef4444" strokeWidth={2} strokeDasharray="5,5" />
            {data.fixation.points.map((point, i) => (
              <circle key={i} cx={point.x} cy={point.y} r={3} fill="#3b82f6" opacity={0.6} />
            ))}
            <text x={20} y={30} className="text-sm font-medium">
              注视稳定性分析
            </text>
          </svg>
        )

      case "pursuit":
        return (
          <svg width={svgWidth} height={svgHeight} className="border rounded-lg bg-gray-50">
            <path
              d={`M ${data.pursuit.path.map((p) => `${p.target.x},${p.target.y}`).join(" L ")}`}
              fill="none"
              stroke="#ef4444"
              strokeWidth={3}
              opacity={0.7}
            />
            <path
              d={`M ${data.pursuit.path.map((p) => `${p.gaze.x},${p.gaze.y}`).join(" L ")}`}
              fill="none"
              stroke="#3b82f6"
              strokeWidth={2}
            />
            <text x={20} y={30} className="text-sm font-medium">
              追随能力分析
            </text>
            <text x={20} y={50} className="text-xs text-gray-600">
              红线：目标轨迹 | 蓝线：眼球轨迹
            </text>
          </svg>
        )

      case "saccade":
        return (
          <svg width={svgWidth} height={svgHeight} className="border rounded-lg bg-gray-50">
            {data.saccade.movements.map((movement, i) => (
              <g key={i}>
                <line
                  x1={movement.start.x}
                  y1={movement.start.y}
                  x2={movement.end.x}
                  y2={movement.end.y}
                  stroke="#8b5cf6"
                  strokeWidth={2}
                  markerEnd="url(#arrowhead)"
                />
                <circle cx={movement.start.x} cy={movement.start.y} r={4} fill="#10b981" />
                <circle cx={movement.end.x} cy={movement.end.y} r={4} fill="#ef4444" />
              </g>
            ))}
            <defs>
              <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#8b5cf6" />
              </marker>
            </defs>
            <text x={20} y={30} className="text-sm font-medium">
              扫视能力分析
            </text>
            <text x={20} y={50} className="text-xs text-gray-600">
              绿点：起始位置 | 红点：结束位置
            </text>
          </svg>
        )

      case "aoi":
        return (
          <svg width={svgWidth} height={svgHeight} className="border rounded-lg bg-gray-50">
            {data.aoi.regions.map((region) => (
              <g key={region.id}>
                <rect
                  x={region.x}
                  y={region.y}
                  width={region.width}
                  height={region.height}
                  fill="#3b82f6"
                  opacity={0.2}
                  stroke="#3b82f6"
                  strokeWidth={2}
                />
                <text
                  x={region.x + region.width / 2}
                  y={region.y + region.height / 2}
                  textAnchor="middle"
                  className="text-sm font-medium"
                >
                  {region.name}
                </text>
                <text
                  x={region.x + region.width / 2}
                  y={region.y + region.height / 2 + 20}
                  textAnchor="middle"
                  className="text-xs text-gray-600"
                >
                  {region.fixations}次注视
                </text>
              </g>
            ))}
            <text x={20} y={30} className="text-sm font-medium">
              兴趣区域检测
            </text>
          </svg>
        )

      default:
        return null
    }
  }

  const getMetrics = (type: string) => {
    switch (type) {
      case "fixation":
        return [
          { label: "稳定性评分", value: `${data.fixation.stability.toFixed(1)}%`, color: "bg-blue-500" },
          { label: "平均持续时间", value: `${data.fixation.duration.toFixed(0)}ms`, color: "bg-green-500" },
          { label: "离散度", value: `${data.fixation.dispersion.toFixed(1)}px`, color: "bg-yellow-500" },
        ]

      case "pursuit":
        return [
          { label: "追随精度", value: `${data.pursuit.accuracy.toFixed(1)}%`, color: "bg-blue-500" },
          { label: "平均速度", value: `${data.pursuit.velocity.toFixed(0)}°/s`, color: "bg-green-500" },
          { label: "延迟时间", value: `${data.pursuit.lag.toFixed(0)}ms`, color: "bg-red-500" },
        ]

      case "saccade":
        return [
          { label: "扫视频率", value: `${data.saccade.frequency.toFixed(1)}/s`, color: "bg-purple-500" },
          { label: "平均幅度", value: `${data.saccade.amplitude.toFixed(1)}°`, color: "bg-blue-500" },
          { label: "峰值速度", value: `${data.saccade.velocity.toFixed(0)}°/s`, color: "bg-green-500" },
        ]

      case "aoi":
        return data.aoi.regions.map((region) => ({
          label: region.name,
          value: `${region.fixations}次 / ${region.duration}ms`,
          color: "bg-indigo-500",
        }))

      default:
        return []
    }
  }

  return (
    <div className="min-h-screen bg-gray-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">眼球运动评估系统</h1>
            <p className="text-gray-600 mt-1">实时监测和分析眼球运动模式</p>
          </div>
          <div className="flex items-center gap-4">
            <Badge variant={isRecording ? "default" : "secondary"} className="px-3 py-1">
              {isRecording ? "正在记录" : "已停止"}
            </Badge>
            <Button
              onClick={() => setIsRecording(!isRecording)}
              variant={isRecording ? "destructive" : "default"}
              className="flex items-center gap-2"
            >
              {isRecording ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
              {isRecording ? "停止" : "开始"}
            </Button>
            <Button variant="outline" size="icon">
              <RotateCcw className="w-4 h-4" />
            </Button>
            <Button variant="outline" size="icon">
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Visualization Area */}
          <div className="lg:col-span-3">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="w-5 h-5" />
                  眼球运动可视化
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs value={activeTab} onValueChange={setActiveTab}>
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="fixation" className="flex items-center gap-2">
                      <Target className="w-4 h-4" />
                      注视稳定性
                    </TabsTrigger>
                    <TabsTrigger value="pursuit" className="flex items-center gap-2">
                      <Move className="w-4 h-4" />
                      追随能力
                    </TabsTrigger>
                    <TabsTrigger value="saccade" className="flex items-center gap-2">
                      <Zap className="w-4 h-4" />
                      扫视能力
                    </TabsTrigger>
                    <TabsTrigger value="aoi" className="flex items-center gap-2">
                      <Eye className="w-4 h-4" />
                      兴趣区域
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value={activeTab} className="mt-6">
                    <div className="flex justify-center">{renderVisualization(activeTab)}</div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>

          {/* Metrics Panel */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>实时指标</CardTitle>
                <CardDescription>
                  {activeTab === "fixation" && "注视稳定性相关指标"}
                  {activeTab === "pursuit" && "追随能力相关指标"}
                  {activeTab === "saccade" && "扫视能力相关指标"}
                  {activeTab === "aoi" && "兴趣区域统计数据"}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {getMetrics(activeTab).map((metric, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">{metric.label}</span>
                      <span className="text-sm text-gray-600">{metric.value}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${metric.color}`}
                        style={{ width: `${Math.random() * 100}%` }}
                      />
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>系统状态</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">眼动仪连接</span>
                  <Badge variant="default">正常</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">校准状态</span>
                  <Badge variant="default">已校准</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">采样率</span>
                  <span className="text-sm text-gray-600">1000Hz</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">数据质量</span>
                  <Progress value={85} className="w-16" />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
