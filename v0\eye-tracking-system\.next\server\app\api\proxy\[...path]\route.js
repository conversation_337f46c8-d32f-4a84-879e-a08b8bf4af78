/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/proxy/[...path]/route";
exports.ids = ["app/api/proxy/[...path]/route"];
exports.modules = {

/***/ "(rsc)/./app/api/proxy/[...path]/route.ts":
/*!******************************************!*\
  !*** ./app/api/proxy/[...path]/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// 目标服务器地址，从环境变量读取，默认为localhost:8080\nconst TARGET_URL = \"http://localhost:8080\" || 0;\n// 处理所有HTTP方法的代理请求\nasync function handleProxy(request, { params }) {\n    try {\n        // 构建目标URL\n        const path = params.path.join('/');\n        const targetUrl = `${TARGET_URL}/${path}`;\n        // 获取查询参数\n        const searchParams = request.nextUrl.searchParams;\n        const queryString = searchParams.toString();\n        const fullUrl = queryString ? `${targetUrl}?${queryString}` : targetUrl;\n        // 获取请求体\n        let body = null;\n        if (request.method !== 'GET' && request.method !== 'HEAD') {\n            try {\n                body = await request.text();\n            } catch (error) {\n                console.error('Error reading request body:', error);\n            }\n        }\n        // 构建请求头，排除一些不需要的头部\n        const headers = {};\n        request.headers.forEach((value, key)=>{\n            // 排除一些可能导致问题的头部\n            if (![\n                'host',\n                'connection',\n                'content-length'\n            ].includes(key.toLowerCase())) {\n                headers[key] = value;\n            }\n        });\n        // 确保Content-Type头部存在\n        if (body && !headers['content-type']) {\n            headers['content-type'] = 'application/json';\n        }\n        console.log(`Proxying ${request.method} request to: ${fullUrl}`);\n        // 发送代理请求\n        const response = await fetch(fullUrl, {\n            method: request.method,\n            headers,\n            body: body || undefined\n        });\n        // 获取响应数据\n        const responseData = await response.text();\n        // 构建响应头，添加CORS头部\n        const responseHeaders = new Headers();\n        // 复制原始响应头\n        response.headers.forEach((value, key)=>{\n            responseHeaders.set(key, value);\n        });\n        // 添加CORS头部\n        responseHeaders.set('Access-Control-Allow-Origin', '*');\n        responseHeaders.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');\n        responseHeaders.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');\n        responseHeaders.set('Access-Control-Max-Age', '86400');\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(responseData, {\n            status: response.status,\n            statusText: response.statusText,\n            headers: responseHeaders\n        });\n    } catch (error) {\n        console.error('Proxy error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Proxy request failed',\n            message: error instanceof Error ? error.message : 'Unknown error',\n            timestamp: Date.now()\n        }, {\n            status: 500,\n            headers: {\n                'Access-Control-Allow-Origin': '*',\n                'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n                'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With'\n            }\n        });\n    }\n}\n// 处理OPTIONS请求（预检请求）\nasync function OPTIONS(request, context) {\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 200,\n        headers: {\n            'Access-Control-Allow-Origin': '*',\n            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n            'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',\n            'Access-Control-Max-Age': '86400'\n        }\n    });\n}\n// 导出所有HTTP方法的处理函数\nasync function GET(request, context) {\n    return handleProxy(request, context);\n}\nasync function POST(request, context) {\n    return handleProxy(request, context);\n}\nasync function PUT(request, context) {\n    return handleProxy(request, context);\n}\nasync function DELETE(request, context) {\n    return handleProxy(request, context);\n}\nasync function PATCH(request, context) {\n    return handleProxy(request, context);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/proxy/[...path]/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproxy%2F%5B...path%5D%2Froute&page=%2Fapi%2Fproxy%2F%5B...path%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproxy%2F%5B...path%5D%2Froute.ts&appDir=D%3A%5Cresearch-ui%5Cv0%5Ceye-tracking-system%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cresearch-ui%5Cv0%5Ceye-tracking-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproxy%2F%5B...path%5D%2Froute&page=%2Fapi%2Fproxy%2F%5B...path%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproxy%2F%5B...path%5D%2Froute.ts&appDir=D%3A%5Cresearch-ui%5Cv0%5Ceye-tracking-system%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cresearch-ui%5Cv0%5Ceye-tracking-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_research_ui_v0_eye_tracking_system_app_api_proxy_path_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/proxy/[...path]/route.ts */ \"(rsc)/./app/api/proxy/[...path]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/proxy/[...path]/route\",\n        pathname: \"/api/proxy/[...path]\",\n        filename: \"route\",\n        bundlePath: \"app/api/proxy/[...path]/route\"\n    },\n    resolvedPagePath: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\app\\\\api\\\\proxy\\\\[...path]\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_research_ui_v0_eye_tracking_system_app_api_proxy_path_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZwcm94eSUyRiU1Qi4uLnBhdGglNUQlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRnByb3h5JTJGJTVCLi4ucGF0aCU1RCUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRnByb3h5JTJGJTVCLi4ucGF0aCU1RCUyRnJvdXRlLnRzJmFwcERpcj1EJTNBJTVDcmVzZWFyY2gtdWklNUN2MCU1Q2V5ZS10cmFja2luZy1zeXN0ZW0lNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUQlM0ElNUNyZXNlYXJjaC11aSU1Q3YwJTVDZXllLXRyYWNraW5nLXN5c3RlbSZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDOEI7QUFDM0c7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlHQUFtQjtBQUMzQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzREFBc0Q7QUFDOUQ7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDMEY7O0FBRTFGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkQ6XFxcXHJlc2VhcmNoLXVpXFxcXHYwXFxcXGV5ZS10cmFja2luZy1zeXN0ZW1cXFxcYXBwXFxcXGFwaVxcXFxwcm94eVxcXFxbLi4ucGF0aF1cXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL3Byb3h5L1suLi5wYXRoXS9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL3Byb3h5L1suLi5wYXRoXVwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvcHJveHkvWy4uLnBhdGhdL3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiRDpcXFxccmVzZWFyY2gtdWlcXFxcdjBcXFxcZXllLXRyYWNraW5nLXN5c3RlbVxcXFxhcHBcXFxcYXBpXFxcXHByb3h5XFxcXFsuLi5wYXRoXVxcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHdvcmtBc3luY1N0b3JhZ2UsXG4gICAgICAgIHdvcmtVbml0QXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproxy%2F%5B...path%5D%2Froute&page=%2Fapi%2Fproxy%2F%5B...path%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproxy%2F%5B...path%5D%2Froute.ts&appDir=D%3A%5Cresearch-ui%5Cv0%5Ceye-tracking-system%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cresearch-ui%5Cv0%5Ceye-tracking-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproxy%2F%5B...path%5D%2Froute&page=%2Fapi%2Fproxy%2F%5B...path%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproxy%2F%5B...path%5D%2Froute.ts&appDir=D%3A%5Cresearch-ui%5Cv0%5Ceye-tracking-system%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cresearch-ui%5Cv0%5Ceye-tracking-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();